'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import AdminHome from './admin-home'

export default function Home() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    console.log('🏠 HomePage - État auth:', {
      loading,
      user: user?.email,
      hasUser: !!user
    })

    if (!loading && !user) {
      console.log('🔄 HomePage - Redirection vers /login (pas d\'utilisateur)')
      router.push('/login')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return <AdminHome />
}
