#!/usr/bin/env python3
"""
Script pour créer UN SEUL utilisateur admin : <EMAIL>
Supprime tous les autres utilisateurs et ne garde que celui-ci
"""

import asyncio
import asyncpg
import uuid
from datetime import datetime
from app.core.config import settings
from app.services.supabase_service import SupabaseService

DATABASE_URL = settings.DATABASE_URL

async def clean_and_create_single_admin():
    """Nettoyer et créer un seul utilisateur admin"""
    print("🧹 Nettoyage et création d'un seul admin...")
    
    admin_email = "<EMAIL>"
    admin_password = "orbis123!"
    
    supabase_service = SupabaseService()
    
    try:
        # 1. Supprimer TOUS les utilisateurs existants dans Supabase
        print("   🗑️ Suppression de tous les utilisateurs Supabase...")
        
        users_data = await supabase_service.list_users()
        for user in users_data.get("users", []):
            user_id = user.get("id")
            user_email = user.get("email", "")
            print(f"     🗑️ Suppression de {user_email} ({user_id[:8]}...)")
            await supabase_service.delete_user(user_id)
        
        print(f"   ✅ {len(users_data.get('users', []))} utilisateur(s) supprimé(s)")
        
        # 2. Nettoyer les tables locales
        print("   🧹 Nettoyage des tables locales...")
        
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        try:
            # Supprimer toutes les données liées aux utilisateurs
            await conn.execute("DELETE FROM user_companies")
            await conn.execute("DELETE FROM user_profiles")
            await conn.execute("DELETE FROM users")
            
            print("   ✅ Tables locales nettoyées")
            
        finally:
            await conn.close()
        
        # 3. Créer le nouvel utilisateur admin unique
        print(f"   👤 Création de l'admin unique : {admin_email}")
        
        user_metadata = {
            "first_name": "Jeremy",
            "last_name": "Giaime",
            "role": "super_admin",
            "company": "JALIS"
        }
        
        supabase_user = await supabase_service.create_user(
            email=admin_email,
            password=admin_password,
            user_metadata=user_metadata
        )
        
        # Extraire l'ID utilisateur
        if "user" in supabase_user:
            supabase_user_id = supabase_user["user"]["id"]
        elif "id" in supabase_user:
            supabase_user_id = supabase_user["id"]
        else:
            # Récupérer depuis la liste
            users_data = await supabase_service.list_users()
            for user in users_data.get("users", []):
                if user.get("email") == admin_email:
                    supabase_user_id = user.get("id")
                    break
            else:
                raise Exception("Impossible de récupérer l'ID utilisateur")
        
        print(f"   ✅ Utilisateur Supabase créé : {supabase_user_id}")
        
        # 4. Créer les entrées dans les tables locales
        print("   📊 Création des entrées locales...")
        
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        try:
            # Créer l'utilisateur dans la table users
            user_id = await conn.fetchval("""
                INSERT INTO users (
                    id, supabase_user_id, email, first_name, last_name, 
                    role, is_active, is_superuser, is_verified, 
                    created_at, updated_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                RETURNING id
            """,
            uuid.uuid4(), supabase_user_id, admin_email, "Jeremy", "Giaime", 
            "SUPER_ADMIN", True, True, True, datetime.utcnow(), datetime.utcnow()
            )
            
            print(f"   ✅ Utilisateur local créé (ID: {user_id})")
            
            # Créer le profil utilisateur
            await conn.execute("""
                INSERT INTO user_profiles (
                    id, first_name, last_name, company, phone, role, 
                    is_active, created_at, updated_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            """,
            supabase_user_id, "Jeremy", "Giaime", "JALIS", "",
            "super_admin", True, datetime.utcnow(), datetime.utcnow()
            )
            
            print(f"   ✅ Profil utilisateur créé")
            
            # Créer l'entreprise JALIS si elle n'existe pas
            company_id = await conn.fetchval("""
                INSERT INTO companies (
                    id, name, code, description, is_active, created_at, updated_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                ON CONFLICT (code) DO UPDATE SET
                    name = EXCLUDED.name,
                    updated_at = EXCLUDED.updated_at
                RETURNING id
            """,
            uuid.uuid4(), "JALIS", "JALIS", "Entreprise JALIS", 
            True, datetime.utcnow(), datetime.utcnow()
            )
            
            print(f"   ✅ Entreprise JALIS créée/mise à jour (ID: {company_id})")
            
            # Associer l'utilisateur à l'entreprise
            await conn.execute("""
                INSERT INTO user_companies (
                    id, user_id, company_id, role, is_active, created_at, updated_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            """,
            uuid.uuid4(), user_id, company_id, "SUPER_ADMIN", 
            True, datetime.utcnow(), datetime.utcnow()
            )
            
            print(f"   ✅ Association utilisateur-entreprise créée")
            
        finally:
            await conn.close()
        
        return supabase_user_id, admin_email, admin_password
        
    except Exception as e:
        print(f"   ❌ Erreur : {e}")
        return None, None, None

async def verify_single_admin():
    """Vérifier qu'il n'y a qu'un seul admin"""
    print("   🔍 Vérification de l'admin unique...")
    
    supabase_service = SupabaseService()
    
    try:
        # Vérifier Supabase
        users_data = await supabase_service.list_users()
        supabase_users = users_data.get("users", [])
        
        print(f"   📊 Utilisateurs Supabase : {len(supabase_users)}")
        for user in supabase_users:
            email = user.get("email", "")
            role = user.get("user_metadata", {}).get("role", "")
            print(f"     • {email} (rôle: {role})")
        
        # Vérifier tables locales
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        try:
            local_users = await conn.fetch("""
                SELECT email, role, is_active, is_superuser
                FROM users
            """)
            
            print(f"   📊 Utilisateurs locaux : {len(local_users)}")
            for user in local_users:
                print(f"     • {user['email']} (rôle: {user['role']}, superuser: {user['is_superuser']})")
            
            profiles = await conn.fetch("""
                SELECT first_name, last_name, role, company
                FROM user_profiles
            """)
            
            print(f"   📊 Profils utilisateurs : {len(profiles)}")
            for profile in profiles:
                print(f"     • {profile['first_name']} {profile['last_name']} ({profile['role']}) - {profile['company']}")
            
        finally:
            await conn.close()
        
        # Vérifier qu'il n'y a qu'un seul utilisateur
        if len(supabase_users) == 1 and len(local_users) == 1:
            admin_user = supabase_users[0]
            if admin_user.get("email") == "<EMAIL>":
                print("   ✅ Admin unique correctement configuré")
                return True
        
        print("   ❌ Configuration incorrecte")
        return False
        
    except Exception as e:
        print(f"   ❌ Erreur de vérification : {e}")
        return False

async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Création d'un Admin Unique")
    print("="*50)
    
    # Nettoyer et créer l'admin unique
    user_id, email, password = await clean_and_create_single_admin()
    
    if user_id:
        print("\n🔍 Vérification...")
        if await verify_single_admin():
            print("\n✅ Admin unique créé avec succès!")
            
            print("\n🔑 IDENTIFIANTS UNIQUES:")
            print("="*40)
            print(f"📧 Email: {email}")
            print(f"🔑 Mot de passe: {password}")
            print(f"👤 Rôle: super_admin")
            print(f"🏢 Entreprise: JALIS")
            print("="*40)
            
            print("\n🎯 Instructions de test:")
            print("   1. Aller sur: http://localhost:3001")
            print("   2. Se connecter avec les identifiants ci-dessus")
            print("   3. Vérifier l'accès aux fonctions admin")
            
            return True
    
    print("\n❌ Échec de la création de l'admin unique")
    return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
