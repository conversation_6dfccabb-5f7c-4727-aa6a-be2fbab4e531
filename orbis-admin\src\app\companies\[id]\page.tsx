'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { 
  Building2, 
  Users, 
  ArrowLeft, 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Calendar,
  Edit,
  Plus,
  UserPlus
} from 'lucide-react'
import { useToast } from '@/components/ui/Toast'
import { FastCompanyService } from '@/lib/fast-auth'

interface Company {
  id: number
  name: string
  code: string
  description?: string
  address?: string
  phone?: string
  email?: string
  website?: string
  siret?: string
  is_active: boolean
  created_at?: string
  user_count: number
}

interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  role: string
  is_active: boolean
}

export default function CompanyDetailPage() {
  const { success, error: showError } = useToast()
  const [company, setCompany] = useState<Company | null>(null)
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [usersLoading, setUsersLoading] = useState(false)
  const params = useParams()
  const router = useRouter()
  const companyId = parseInt(params.id as string)

  useEffect(() => {
    if (companyId) {
      loadCompanyDetails()
      loadCompanyUsers()
    }
  }, [companyId])

  const loadCompanyDetails = async () => {
    try {
      setLoading(true)
      console.log('🔄 Chargement détails entreprise:', companyId)
      const companiesData = await FastCompanyService.getCompanies()
      const foundCompany = companiesData.find(c => c.id === companyId)
      if (foundCompany) {
        console.log('✅ Entreprise trouvée:', foundCompany.name)
        setCompany(foundCompany)
      } else {
        console.log('❌ Entreprise non trouvée')
        showError('Erreur', 'Entreprise non trouvée')
        router.push('/companies')
      }
    } catch (error) {
      console.error('❌ Erreur chargement détails:', error)
      showError('Erreur', 'Impossible de charger les détails de l\'entreprise')
    } finally {
      setLoading(false)
    }
  }

  const loadCompanyUsers = async () => {
    try {
      setUsersLoading(true)
      console.log('🔄 Chargement utilisateurs entreprise:', companyId)
      const usersData = await FastCompanyService.getCompanyUsers(companyId)
      console.log('✅ Utilisateurs chargés:', usersData.length)
      setUsers(usersData)
    } catch (error) {
      console.error('❌ Erreur chargement utilisateurs:', error)
      showError('Erreur', 'Impossible de charger les administrateurs')
    } finally {
      setUsersLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement des détails de l'entreprise...</p>
        </div>
      </div>
    )
  }

  if (!company) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Entreprise non trouvée</h3>
          <p className="text-gray-600 mb-4">L'entreprise demandée n'existe pas ou a été supprimée.</p>
          <button 
            onClick={() => router.push('/companies')}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Retour à la liste
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Back Button */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => router.push('/companies')}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          title="Retour à la liste"
        >
          <ArrowLeft className="w-5 h-5 text-gray-600" />
        </button>
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Building2 className="w-8 h-8 text-green-600" />
            {company.name}
          </h1>
          <p className="text-gray-600 mt-1">
            Détails de l'entreprise et gestion des administrateurs
          </p>
        </div>
        <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
          <Edit className="w-4 h-4" />
          Modifier
        </button>
      </div>

      {/* Company Information Card */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6">
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center">
                <Building2 className="w-8 h-8 text-green-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">{company.name}</h2>
                <p className="text-gray-600">{company.code}</p>
                <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium mt-2 ${
                  company.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {company.is_active ? 'Actif' : 'Inactif'}
                </span>
              </div>
            </div>
          </div>

          {/* Description */}
          {company.description && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Description</h3>
              <p className="text-gray-600">{company.description}</p>
            </div>
          )}

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Informations de contact</h3>
              <div className="space-y-3">
                {company.address && (
                  <div className="flex items-start gap-3">
                    <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                    <span className="text-gray-600">{company.address}</span>
                  </div>
                )}
                {company.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">{company.phone}</span>
                  </div>
                )}
                {company.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="w-4 h-4 text-gray-400" />
                    <a href={`mailto:${company.email}`} className="text-green-600 hover:text-green-700">
                      {company.email}
                    </a>
                  </div>
                )}
                {company.website && (
                  <div className="flex items-center gap-3">
                    <Globe className="w-4 h-4 text-gray-400" />
                    <a 
                      href={company.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-green-600 hover:text-green-700"
                    >
                      {company.website}
                    </a>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Informations légales</h3>
              <div className="space-y-3">
                {company.siret && (
                  <div>
                    <span className="text-sm text-gray-500">SIRET</span>
                    <p className="text-gray-600">{company.siret}</p>
                  </div>
                )}
                <div>
                  <span className="text-sm text-gray-500">Date de création</span>
                  <p className="text-gray-600 flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    {company.created_at ? new Date(company.created_at).toLocaleDateString('fr-FR') : 'Non définie'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Administrators Section */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Users className="w-5 h-5 text-green-600" />
                Administrateurs ({users.length})
              </h2>
              <p className="text-gray-600 text-sm mt-1">
                Gérez les administrateurs de cette entreprise
              </p>
            </div>
            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
              <UserPlus className="w-4 h-4" />
              Ajouter un admin
            </button>
          </div>

          {usersLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
              <span className="ml-3 text-gray-600">Chargement des administrateurs...</span>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun administrateur</h3>
              <p className="text-gray-600 mb-4">
                Cette entreprise n'a pas encore d'administrateur assigné.
              </p>
              <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 mx-auto transition-colors">
                <UserPlus className="w-4 h-4" />
                Ajouter le premier admin
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {users.map((user) => (
                <div key={user.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 font-medium">
                        {user.email.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-gray-900 truncate">{user.email}</p>
                      <p className="text-sm text-gray-600 truncate">
                        {user.first_name} {user.last_name}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      user.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {user.is_active ? 'Actif' : 'Inactif'}
                    </span>
                    <span className="text-xs text-gray-500">
                      {user.role || 'Admin'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
