'use client'

import { useAuth } from '@/contexts/AuthContext'
import Link from 'next/link'
import {
  Building2,
  Users,
  BarChart3,
  Shield,
  Settings,
  Activity,
  TrendingUp,
  AlertCircle,
  Plus,
  ArrowUpRight,
  Clock,
  CheckCircle
} from 'lucide-react'
import ProtectedRoute from '@/components/ProtectedRoute'

export default function AdminHome() {
  const { user } = useAuth()

  const adminModules = [
    {
      title: "Gestion des Entreprises",
      description: "Créer, modifier et gérer les entreprises clientes",
      icon: Building2,
      href: "/companies",
      color: "from-green-500 to-emerald-600",
      stats: "12 entreprises"
    },
    {
      title: "Gestion des Utilisateurs",
      description: "Inviter et gérer les utilisateurs par entreprise",
      icon: Users,
      href: "/users",
      color: "from-emerald-500 to-green-600",
      stats: "48 utilisateurs"
    },
    {
      title: "Analytics & Rapports",
      description: "Statistiques d'utilisation et rapports globaux",
      icon: BarChart3,
      href: "/analytics",
      color: "from-green-600 to-emerald-700",
      stats: "Voir les données"
    },
    {
      title: "Sécurité & Audit",
      description: "Logs d'audit et gestion de la sécurité",
      icon: Shield,
      href: "/security",
      color: "from-emerald-600 to-green-700",
      stats: "Tout sécurisé"
    },
    {
      title: "Configuration Système",
      description: "Paramètres globaux et configuration",
      icon: Settings,
      href: "/settings",
      color: "from-green-700 to-emerald-800",
      stats: "Configurer"
    }
  ]

  const quickStats = [
    {
      title: "Entreprises Actives",
      value: "12",
      change: "+2",
      changeType: "increase",
      icon: Building2
    },
    {
      title: "Utilisateurs Total",
      value: "48",
      change: "+5",
      changeType: "increase",
      icon: Users
    },
    {
      title: "Projets en Cours",
      value: "23",
      change: "+3",
      changeType: "increase",
      icon: Activity
    },
    {
      title: "Taux d'Activité",
      value: "94%",
      change: "+1.2%",
      changeType: "increase",
      icon: TrendingUp
    }
  ]

  const recentActivities = [
    {
      title: "Nouvelle entreprise créée",
      description: "Construction Moderne SARL",
      time: "Il y a 5 minutes",
      type: "success"
    },
    {
      title: "Utilisateur invité",
      description: "<EMAIL>",
      time: "Il y a 1 heure",
      type: "info"
    },
    {
      title: "Projet terminé",
      description: "Rénovation Bureau Central",
      time: "Il y a 2 heures",
      type: "success"
    },
    {
      title: "Maintenance système",
      description: "Mise à jour de sécurité appliquée",
      time: "Il y a 3 heures",
      type: "warning"
    }
  ]

  return (
    <ProtectedRoute requireAdmin={true}>
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">
                Bienvenue, {user?.user_metadata?.name || 'Admin'} 👋
              </h1>
              <p className="text-green-100 text-lg">
                Gérez votre plateforme ORBIS depuis ce tableau de bord centralisé
              </p>
            </div>
            <div className="hidden md:block">
              <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
                <Building2 className="w-12 h-12 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickStats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <div key={index} className="modern-card p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
                    <div className="flex items-center mt-2">
                      <span className={`text-sm font-medium ${
                        stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </span>
                      <span className="text-sm text-gray-500 ml-1">ce mois</span>
                    </div>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <IconComponent className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Main Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Admin Modules - 2/3 width */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Modules d'Administration</h2>
              <button className="btn-outline">
                <Plus className="w-4 h-4 mr-2" />
                Nouveau
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {adminModules.map((module) => {
                const IconComponent = module.icon
                return (
                  <Link
                    key={module.href}
                    href={module.href}
                    className="group modern-card p-6 hover:scale-105 transition-all duration-300"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className={`w-12 h-12 bg-gradient-to-r ${module.color} rounded-xl flex items-center justify-center shadow-lg`}>
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <ArrowUpRight className="w-5 h-5 text-gray-400 group-hover:text-green-600 transition-colors" />
                    </div>

                    <h3 className="text-lg font-semibold text-gray-900 group-hover:text-green-600 transition-colors mb-2">
                      {module.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-3">
                      {module.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="badge-success">
                        {module.stats}
                      </span>
                    </div>
                  </Link>
                )
              })}
            </div>
          </div>

          {/* Sidebar - 1/3 width */}
          <div className="space-y-6">
            {/* Recent Activities */}
            <div className="modern-card p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Activité Récente</h3>
                <Clock className="w-5 h-5 text-gray-400" />
              </div>

              <div className="space-y-4">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className={`w-2 h-2 rounded-full mt-2 ${
                      activity.type === 'success' ? 'bg-green-500' :
                      activity.type === 'warning' ? 'bg-yellow-500' :
                      'bg-blue-500'
                    }`} />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                      <p className="text-xs text-gray-500 truncate">{activity.description}</p>
                      <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>

              <button className="w-full mt-4 text-sm text-green-600 hover:text-green-700 font-medium">
                Voir toute l'activité
              </button>
            </div>

            {/* Quick Actions */}
            <div className="modern-card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions Rapides</h3>

              <div className="space-y-3">
                <Link
                  href="/companies/new"
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-green-50 transition-colors group"
                >
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                    <Plus className="w-4 h-4 text-green-600" />
                  </div>
                  <span className="text-sm font-medium text-gray-900">Nouvelle Entreprise</span>
                </Link>

                <Link
                  href="/users/invite"
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-green-50 transition-colors group"
                >
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                    <Users className="w-4 h-4 text-green-600" />
                  </div>
                  <span className="text-sm font-medium text-gray-900">Inviter Utilisateur</span>
                </Link>

                <Link
                  href="/analytics"
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-green-50 transition-colors group"
                >
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                    <BarChart3 className="w-4 h-4 text-green-600" />
                  </div>
                  <span className="text-sm font-medium text-gray-900">Voir Analytics</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
