# app/models/company.py
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, DateTime, ForeignKey, Text, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.core.database import Base

class CompanyRole(str, enum.Enum):
    ADMIN = "admin"          # Full access to company
    MANAGER = "manager"      # Manage projects and users
    USER = "user"           # Standard user access
    VIEWER = "viewer"       # Read-only access

class Company(Base):
    __tablename__ = "companies"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    code = Column(String, unique=True, nullable=False, index=True)
    description = Column(Text)
    address = Column(Text)
    phone = Column(String)
    email = Column(String)
    website = Column(String)
    siret = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    users = relationship("UserCompany", back_populates="company")
    settings = relationship("CompanySettings", back_populates="company", uselist=False)
    projects = relationship("Project", back_populates="company")
    employees = relationship("Employee", back_populates="company")
    suppliers = relationship("Supplier", back_populates="company")
    materials = relationship("Material", back_populates="company")
    budgets = relationship("Budget", back_populates="company")
    purchase_orders = relationship("PurchaseOrder", back_populates="company")
    quotes = relationship("Quote", back_populates="company")
    documents = relationship("Document", back_populates="company")

class UserCompany(Base):
    __tablename__ = "user_companies"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    role = Column(Enum(CompanyRole), nullable=False, default=CompanyRole.USER)
    is_default = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    invited_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    invited_at = Column(DateTime, nullable=True)
    joined_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="companies", foreign_keys=[user_id])
    company = relationship("Company", back_populates="users")
    inviter = relationship("User", foreign_keys=[invited_by])

class CompanySettings(Base):
    __tablename__ = "company_settings"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    default_currency = Column(String, default="EUR")
    date_format = Column(String, default="DD/MM/YYYY")
    time_format = Column(String, default="24h")
    language = Column(String, default="fr")
    logo_url = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    company = relationship("Company", back_populates="settings")