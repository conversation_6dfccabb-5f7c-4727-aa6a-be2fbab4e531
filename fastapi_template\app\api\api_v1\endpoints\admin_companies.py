# app/api/api_v1/endpoints/admin_companies.py
from typing import Any, List, Dict
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel

from app.core.database import get_db
from app.core.jwt_auth import require_auth, require_superuser
from app.models.user import User
from app.models.company import Company, UserCompany, CompanySettings
from app.schemas.company import Company as CompanySchema, CompanyCreate, CompanyUpdate

router = APIRouter()

# Les dependencies sont maintenant dans jwt_auth.py

# Pydantic models pour l'admin
class AdminCompanyResponse(BaseModel):
    id: int
    name: str
    code: str
    description: str = None
    address: str = None
    phone: str = None
    email: str = None
    website: str = None
    is_active: bool
    created_at: str
    updated_at: str
    user_count: int = 0

    class Config:
        from_attributes = True

@router.get("/", response_model=List[AdminCompanyResponse])
async def list_all_companies(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: Dict[str, Any] = Depends(require_superuser)
):
    """
    Lister toutes les entreprises (super admin seulement)
    """
    try:
        # Récupérer toutes les entreprises
        result = await db.execute(
            select(Company).offset(skip).limit(limit)
        )
        companies = result.scalars().all()
        
        # Pour chaque entreprise, compter le nombre d'utilisateurs
        companies_with_count = []
        for company in companies:
            user_count_result = await db.execute(
                select(UserCompany).where(UserCompany.company_id == company.id)
            )
            user_count = len(user_count_result.scalars().all())
            
            company_data = AdminCompanyResponse(
                id=company.id,
                name=company.name,
                code=company.code,
                description=company.description,
                address=company.address,
                phone=company.phone,
                email=company.email,
                website=company.website,
                is_active=company.is_active,
                created_at=company.created_at.isoformat() if company.created_at else "",
                updated_at=company.updated_at.isoformat() if company.updated_at else "",
                user_count=user_count
            )
            companies_with_count.append(company_data)
        
        return companies_with_count
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch companies: {str(e)}"
        )

@router.post("/", response_model=AdminCompanyResponse)
async def create_company(
    company_data: CompanyCreate,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_superuser)
):
    """
    Créer une nouvelle entreprise (super admin seulement)
    """
    try:
        # Vérifier si le code existe déjà
        result = await db.execute(
            select(Company).where(Company.code == company_data.code)
        )
        existing_company = result.scalar_one_or_none()
        
        if existing_company:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Company code already exists"
            )
        
        # Créer l'entreprise
        company = Company(**company_data.dict())
        db.add(company)
        await db.commit()
        await db.refresh(company)
        
        return AdminCompanyResponse(
            id=company.id,
            name=company.name,
            code=company.code,
            description=company.description,
            address=company.address,
            phone=company.phone,
            email=company.email,
            website=company.website,
            is_active=company.is_active,
            created_at=company.created_at.isoformat() if company.created_at else "",
            updated_at=company.updated_at.isoformat() if company.updated_at else "",
            user_count=0
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create company: {str(e)}"
        )

@router.put("/{company_id}", response_model=AdminCompanyResponse)
async def update_company(
    company_id: int,
    company_data: CompanyUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_superuser)
):
    """
    Mettre à jour une entreprise (super admin seulement)
    """
    try:
        # Récupérer l'entreprise
        result = await db.execute(
            select(Company).where(Company.id == company_id)
        )
        company = result.scalar_one_or_none()
        
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )
        
        # Mettre à jour les champs
        update_data = company_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(company, field, value)
        
        await db.commit()
        await db.refresh(company)
        
        # Compter les utilisateurs
        user_count_result = await db.execute(
            select(UserCompany).where(UserCompany.company_id == company.id)
        )
        user_count = len(user_count_result.scalars().all())
        
        return AdminCompanyResponse(
            id=company.id,
            name=company.name,
            code=company.code,
            description=company.description,
            address=company.address,
            phone=company.phone,
            email=company.email,
            website=company.website,
            is_active=company.is_active,
            created_at=company.created_at.isoformat() if company.created_at else "",
            updated_at=company.updated_at.isoformat() if company.updated_at else "",
            user_count=user_count
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update company: {str(e)}"
        )

@router.delete("/{company_id}")
async def delete_company(
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_superuser)
):
    """
    Supprimer une entreprise (super admin seulement)
    """
    try:
        # Récupérer l'entreprise
        result = await db.execute(
            select(Company).where(Company.id == company_id)
        )
        company = result.scalar_one_or_none()
        
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )
        
        # Vérifier s'il y a des utilisateurs associés
        user_count_result = await db.execute(
            select(UserCompany).where(UserCompany.company_id == company_id)
        )
        user_companies = user_count_result.scalars().all()
        
        if user_companies:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot delete company with {len(user_companies)} associated users"
            )
        
        # Supprimer l'entreprise
        await db.delete(company)
        await db.commit()
        
        return {"success": True, "message": "Company deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete company: {str(e)}"
        )

@router.post("/{company_id}/toggle-status", response_model=AdminCompanyResponse)
async def toggle_company_status(
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_superuser)
):
    """
    Activer/désactiver une entreprise (super admin seulement)
    """
    try:
        # Récupérer l'entreprise
        result = await db.execute(
            select(Company).where(Company.id == company_id)
        )
        company = result.scalar_one_or_none()
        
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )
        
        # Toggle le statut
        company.is_active = not company.is_active
        await db.commit()
        await db.refresh(company)
        
        # Compter les utilisateurs
        user_count_result = await db.execute(
            select(UserCompany).where(UserCompany.company_id == company.id)
        )
        user_count = len(user_count_result.scalars().all())
        
        return AdminCompanyResponse(
            id=company.id,
            name=company.name,
            code=company.code,
            description=company.description,
            address=company.address,
            phone=company.phone,
            email=company.email,
            website=company.website,
            is_active=company.is_active,
            created_at=company.created_at.isoformat() if company.created_at else "",
            updated_at=company.updated_at.isoformat() if company.updated_at else "",
            user_count=user_count
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to toggle company status: {str(e)}"
        )
