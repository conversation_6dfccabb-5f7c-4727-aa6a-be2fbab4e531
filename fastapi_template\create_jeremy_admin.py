#!/usr/bin/env python3
"""
Script simple pour créer l'<NAME_EMAIL>
"""

import asyncio
import asyncpg
import uuid
from datetime import datetime
from app.core.config import settings
from app.services.supabase_service import SupabaseService

DATABASE_URL = settings.DATABASE_URL

async def create_jeremy_admin():
    """Créer l'utilisateur admin Jeremy"""
    print("👤 Création de l'admin Jeremy...")
    
    admin_email = "<EMAIL>"
    admin_password = "orbis123!"
    
    supabase_service = SupabaseService()
    
    try:
        # 1. Créer l'utilisateur dans Supabase
        print(f"   🔐 Création dans Supabase : {admin_email}")
        
        user_metadata = {
            "first_name": "<PERSON>",
            "last_name": "Gia<PERSON>",
            "role": "super_admin",
            "company": "JALIS"
        }
        
        try:
            supabase_user = await supabase_service.create_user(
                email=admin_email,
                password=admin_password,
                user_metadata=user_metadata
            )
            
            # Extraire l'ID utilisateur
            if "user" in supabase_user:
                supabase_user_id = supabase_user["user"]["id"]
            elif "id" in supabase_user:
                supabase_user_id = supabase_user["id"]
            else:
                # Récupérer depuis la liste
                users_data = await supabase_service.list_users()
                for user in users_data.get("users", []):
                    if user.get("email") == admin_email:
                        supabase_user_id = user.get("id")
                        break
                else:
                    raise Exception("Impossible de récupérer l'ID utilisateur")
            
            print(f"   ✅ Utilisateur Supabase créé : {supabase_user_id[:8]}...")
            
        except Exception as e:
            if "email_exists" in str(e) or "already exists" in str(e):
                print("   ⚠️ Utilisateur existe déjà dans Supabase, récupération...")
                users_data = await supabase_service.list_users()
                for user in users_data.get("users", []):
                    if user.get("email") == admin_email:
                        supabase_user_id = user.get("id")
                        print(f"   ✅ Utilisateur récupéré : {supabase_user_id[:8]}...")
                        break
                else:
                    raise Exception("Utilisateur existe mais introuvable")
            else:
                raise e
        
        # 2. Créer/mettre à jour dans les tables locales
        print("   📊 Synchronisation avec les tables locales...")
        
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        try:
            # Vérifier si l'utilisateur existe déjà localement
            existing_user = await conn.fetchrow("""
                SELECT id FROM users WHERE email = $1 OR supabase_user_id = $2
            """, admin_email, supabase_user_id)
            
            if existing_user:
                # Mettre à jour l'utilisateur existant
                await conn.execute("""
                    UPDATE users SET
                        supabase_user_id = $1,
                        email = $2,
                        first_name = $3,
                        last_name = $4,
                        role = $5,
                        is_active = $6,
                        is_superuser = $7,
                        is_verified = $8,
                        updated_at = $9
                    WHERE id = $10
                """,
                supabase_user_id, admin_email, "Jeremy", "Giaime", 
                "SUPER_ADMIN", True, True, True, datetime.utcnow(), existing_user['id']
                )
                user_id = existing_user['id']
                print(f"   ✅ Utilisateur local mis à jour")
            else:
                # Créer un nouvel utilisateur
                user_id = await conn.fetchval("""
                    INSERT INTO users (
                        id, supabase_user_id, email, first_name, last_name, 
                        role, is_active, is_superuser, is_verified, 
                        created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                    RETURNING id
                """,
                uuid.uuid4(), supabase_user_id, admin_email, "Jeremy", "Giaime", 
                "SUPER_ADMIN", True, True, True, datetime.utcnow(), datetime.utcnow()
                )
                print(f"   ✅ Utilisateur local créé")
            
            # Créer/mettre à jour le profil utilisateur
            existing_profile = await conn.fetchrow("""
                SELECT id FROM user_profiles WHERE id = $1
            """, supabase_user_id)
            
            if existing_profile:
                await conn.execute("""
                    UPDATE user_profiles SET
                        first_name = $1,
                        last_name = $2,
                        company = $3,
                        role = $4,
                        is_active = $5,
                        updated_at = $6
                    WHERE id = $7
                """,
                "Jeremy", "Giaime", "JALIS", "super_admin", True, datetime.utcnow(), supabase_user_id
                )
                print(f"   ✅ Profil utilisateur mis à jour")
            else:
                await conn.execute("""
                    INSERT INTO user_profiles (
                        id, first_name, last_name, company, phone, role, 
                        is_active, created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                """,
                supabase_user_id, "Jeremy", "Giaime", "JALIS", "",
                "super_admin", True, datetime.utcnow(), datetime.utcnow()
                )
                print(f"   ✅ Profil utilisateur créé")
            
            # Créer l'entreprise JALIS si elle n'existe pas
            existing_company = await conn.fetchrow("""
                SELECT id FROM companies WHERE code = $1
            """, "JALIS")
            
            if existing_company:
                company_id = existing_company['id']
                print(f"   ✅ Entreprise JALIS existe déjà")
            else:
                company_id = await conn.fetchval("""
                    INSERT INTO companies (
                        id, name, code, description, is_active, created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    RETURNING id
                """,
                uuid.uuid4(), "JALIS", "JALIS", "Entreprise JALIS", 
                True, datetime.utcnow(), datetime.utcnow()
                )
                print(f"   ✅ Entreprise JALIS créée")
            
            # Associer l'utilisateur à l'entreprise
            existing_association = await conn.fetchrow("""
                SELECT id FROM user_companies WHERE user_id = $1 AND company_id = $2
            """, user_id, company_id)
            
            if not existing_association:
                await conn.execute("""
                    INSERT INTO user_companies (
                        id, user_id, company_id, role, is_active, created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                uuid.uuid4(), user_id, company_id, "SUPER_ADMIN", 
                True, datetime.utcnow(), datetime.utcnow()
                )
                print(f"   ✅ Association utilisateur-entreprise créée")
            else:
                print(f"   ✅ Association utilisateur-entreprise existe déjà")
            
        finally:
            await conn.close()
        
        return supabase_user_id, admin_email, admin_password
        
    except Exception as e:
        print(f"   ❌ Erreur : {e}")
        return None, None, None

async def verify_jeremy_admin():
    """Vérifier que Jeremy admin est bien configuré"""
    print("   🔍 Vérification de l'admin Jeremy...")
    
    admin_email = "<EMAIL>"
    
    try:
        # Vérifier Supabase
        supabase_service = SupabaseService()
        users_data = await supabase_service.list_users()
        
        jeremy_supabase = None
        for user in users_data.get("users", []):
            if user.get("email") == admin_email:
                jeremy_supabase = user
                break
        
        if not jeremy_supabase:
            print("   ❌ Jeremy non trouvé dans Supabase")
            return False
        
        role = jeremy_supabase.get("user_metadata", {}).get("role", "")
        print(f"   ✅ Jeremy trouvé dans Supabase (rôle: {role})")
        
        # Vérifier tables locales
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        try:
            jeremy_local = await conn.fetchrow("""
                SELECT email, role, is_superuser, is_active
                FROM users
                WHERE email = $1
            """, admin_email)
            
            if not jeremy_local:
                print("   ❌ Jeremy non trouvé dans la table users")
                return False
            
            print(f"   ✅ Jeremy trouvé localement (rôle: {jeremy_local['role']}, superuser: {jeremy_local['is_superuser']})")
            
            jeremy_profile = await conn.fetchrow("""
                SELECT first_name, last_name, role, company
                FROM user_profiles
                WHERE id = $1
            """, jeremy_supabase.get("id"))
            
            if jeremy_profile:
                print(f"   ✅ Profil Jeremy trouvé ({jeremy_profile['role']} - {jeremy_profile['company']})")
            else:
                print("   ⚠️ Profil Jeremy non trouvé")
            
        finally:
            await conn.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur de vérification : {e}")
        return False

async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Création de l'Admin Jeremy")
    print("="*40)
    
    # Créer l'admin Jeremy
    user_id, email, password = await create_jeremy_admin()
    
    if user_id:
        print("\n🔍 Vérification...")
        if await verify_jeremy_admin():
            print("\n✅ Admin Jeremy créé avec succès!")
            
            print("\n🔑 IDENTIFIANTS:")
            print("="*30)
            print(f"📧 Email: {email}")
            print(f"🔑 Mot de passe: {password}")
            print(f"👤 Rôle: super_admin")
            print(f"🏢 Entreprise: JALIS")
            print("="*30)
            
            print("\n🎯 Test de connexion:")
            print("   1. Aller sur: http://localhost:3001")
            print("   2. Se connecter avec les identifiants ci-dessus")
            
            return True
    
    print("\n❌ Échec de la création de l'admin Jeremy")
    return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
