@import "tailwindcss";

/* ORBIS Admin - Design System */
:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Green Color Palette - Primary */
  --green-50: #f0fdf4;
  --green-100: #dcfce7;
  --green-200: #bbf7d0;
  --green-300: #86efac;
  --green-400: #4ade80;
  --green-500: #22c55e;
  --green-600: #16a34a;
  --green-700: #15803d;
  --green-800: #166534;
  --green-900: #14532d;
  --green-950: #052e16;

  /* Emerald Color Palette - Secondary */
  --emerald-50: #ecfdf5;
  --emerald-100: #d1fae5;
  --emerald-200: #a7f3d0;
  --emerald-300: #6ee7b7;
  --emerald-400: #34d399;
  --emerald-500: #10b981;
  --emerald-600: #059669;
  --emerald-700: #047857;
  --emerald-800: #065f46;
  --emerald-900: #064e3b;
  --emerald-950: #022c22;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

/* Modern Components */
@layer components {
  .modern-card {
    @apply bg-white rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300;
  }

  .sidebar-item {
    @apply flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200;
  }

  .sidebar-item.active {
    @apply bg-green-50 text-green-700 border-l-4 border-green-600 pl-2;
  }

  .sidebar-item:not(.active) {
    @apply text-gray-700 hover:bg-gray-50 hover:text-green-600;
  }

  .btn-primary {
    @apply bg-green-600 text-white hover:bg-green-700 active:bg-green-800 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 px-4 py-2 rounded-lg font-medium transition-all duration-200;
  }

  .btn-secondary {
    @apply bg-emerald-100 text-emerald-700 hover:bg-emerald-200 active:bg-emerald-300 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 px-4 py-2 rounded-lg font-medium transition-all duration-200;
  }

  .btn-outline {
    @apply border border-green-300 bg-white text-green-700 hover:border-green-400 hover:bg-green-50 active:bg-green-100 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 px-4 py-2 rounded-lg font-medium transition-all duration-200;
  }

  .form-input {
    @apply rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-2 focus:ring-green-500/25 transition-all duration-200;
  }

  .badge-success {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800;
  }

  .badge-info {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800;
  }
}

/* Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}
