# app/core/config.py
from pydantic_settings import BaseSettings
from pydantic import PostgresDsn
from typing import Optional, List
import secrets
import os

class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "ORBIS Suivi Travaux SAAS"
    
    # Supabase Configuration
    SUPABASE_URL: str = "https://ckqxfylgfcbutcwvqepp.supabase.co"
    SUPABASE_ANON_KEY: str = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNrcXhmeWxnZmNidXRjd3ZxZXBwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMjEzOTcsImV4cCI6MjA2NjU5NzM5N30.ic9RY0xRUFds3BYdIvpL0YcH5jZIgWDd96QihS8C_t4"
    SUPABASE_SERVICE_ROLE_KEY: str = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNrcXhmeWxnZmNidXRjd3ZxZXBwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAyMTM5NywiZXhwIjoyMDY2NTk3Mzk3fQ.BCPC-CYoqZ8GNkOIRPw1UhzdqNuBGEx7-ckr1a05Ggc"

    # Database - PostgreSQL with Supabase
    DATABASE_URL: str = "**************************************************************************************************/postgres"
    # AsyncPG version for SQLAlchemy async operations
    ASYNC_DATABASE_URL: str = "postgresql+asyncpg://postgres.ckqxfylgfcbutcwvqepp:E5FACUUHRbaX&@aws-0-eu-north-1.pooler.supabase.com:6543/postgres"
    DATABASE_ECHO: bool = False
    
    # Security
    SECRET_KEY: str = os.getenv("SECRET_KEY", secrets.token_urlsafe(32))
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    
    # CORS - Configuration dynamique via variables d'environnement
    @property
    def BACKEND_CORS_ORIGINS(self) -> List[str]:
        """
        Configuration CORS dynamique basée sur les variables d'environnement
        Construit les URLs en utilisant les ports définis dans les variables
        """
        # Récupérer les ports depuis les variables d'environnement
        api_port = os.getenv("API_PORT", "8000")
        frontend_port = os.getenv("FRONTEND_PORT", "3001")  # orbis-admin
        customer_port = os.getenv("CUSTOMER_PORT", "3000")  # orbis-frontend

        # URLs de développement (construites dynamiquement)
        dev_origins = [
            f"http://localhost:{customer_port}",      # orbis-frontend
            f"http://localhost:{frontend_port}",      # orbis-admin
            f"http://localhost:{api_port}",           # API backend
            f"https://localhost:{customer_port}",     # HTTPS versions
            f"https://localhost:{frontend_port}",
            f"https://localhost:{api_port}",
        ]

        # URLs de production (depuis variable d'environnement)
        prod_origins_str = os.getenv("BACKEND_CORS_ORIGINS_PROD", "")
        prod_origins = []
        if prod_origins_str:
            prod_origins = [origin.strip() for origin in prod_origins_str.split(",") if origin.strip()]

        # Combiner développement + production
        all_origins = dev_origins + prod_origins

        return all_origins
    
    # Redis
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: Optional[str] = None
    REDIS_URL: str = "redis://localhost:6379"
    
    # File Storage
    UPLOAD_FOLDER: str = "uploads"
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    
    # Multi-tenant
    MAX_COMPANIES: int = 10
    MAX_RECORDS_PER_ENTITY: int = 999999
    
    # Environment
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    DEBUG: bool = os.getenv("DEBUG", "true").lower() == "true"
    
    class Config:
        case_sensitive = True
        env_file = ".env"

settings = Settings()